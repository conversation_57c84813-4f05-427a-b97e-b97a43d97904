version: '3.8'

services:
  core-parser-dev:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"  # React前端
      - "9999:9999"  # Express后端
    volumes:
      - .:/opt/workspace
      - /opt/workspace/node_modules
      - /opt/workspace/server/node_modules
      - /opt/workspace/client/node_modules
    environment:
      - NODE_ENV=development
    command: npm run dev
    stdin_open: true
    tty: true

  core-parser-prod:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "9999:9999"  # 生产环境只暴露后端端口
    environment:
      - NODE_ENV=production
    restart: unless-stopped
